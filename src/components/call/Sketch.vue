<template>
  <section class="sketch">

    <template v-if="!searchMode">
      <div class="_attribute -reference" key="reference" v-if="shouldShowReferenceNumber">
        <div class="_name">Reference</div>
        <div class="_value">{{ call.vc50UserDefined3 || '&mdash;' }}</div>
      </div>
      <div class="_attribute -call" key="call">
        <div class="_name is-small">Call</div>
        <div class="_value">{{ callKey }}</div>
      </div>
      <div class="_attribute -status" key="status">
        <div class="_name is-small">Status</div>
        <div class="_value">{{ callStatus }}</div>
      </div>
      <pre>sale: {{ priceRepository.sale.Total }};
tow: {{ priceRepository.tow.Total }};</pre>
      <div class="_attribute -price" key="price">
        <div class="_name is-small">Price</div>
        <div class="_value">{{ priceProxy | usd }}</div>
      </div>
      <pre>live: {{ liveBalance }};
tc: {{ tcBalance }};</pre>
      <div class="_attribute -balance" key="balance">
        <div class="_name is-small">Balance</div>
        <div class="_value">{{ balanceProxy | usd }}</div>
      </div>

      <div class="_attribute _indicator -holds" v-if="hasHolds" @click="lookAt('holds')" key="holds">
        <i class="_icon far fa-hand-paper"></i> Hold
      </div>
      <div class="_attribute _indicator -notes" v-if="notesProxy.length > 0" @click="lookAt('notes')" key="notes">
        <i class="_icon far fa-sticky-note"></i> {{ notesProxy.length }} notes
      </div>
      <div class="_attribute _indicator -images" v-if="imagesProxy.length > 0" @click="lookAt('images')" key="images">
        <i class="_icon far fa-folder-image"></i> {{ imagesProxy.length }} images
      </div>
      <div class="_attribute _indicator -default" v-if="hasDmvAccess" @click="onShowDMVModal" key="dmv">
        <i class="_icon far fa-steering-wheel"></i> DMV
      </div>
    </template>

    <template v-if="searchMode">
      <span id="call-section">
        <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-12 is-left">
              <app-text v-model="call.lCallKey">
                Call Number
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-select v-model="call.lCallStatusTypeKey" :options="callStatuses" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
                Status
              </app-select>
            </div>
            <div class="column is-12 is-left is-bottom">
              <app-text v-model="call.tcPrice" id="CAL_tcPrice">
                Price
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </span>
    </template>

  </section>
</template>

<script>
import Access from '@/utils/access.js';
import { COMPANY_ID } from '@/config.js';

export default {
  name: 'call-sketch',

  props: {
    call: {
      type: Object,
      required: true
    },
    liveBalance: {
      type: Number,
      default: 0
    },
    priceRepository: {
      type: Object,
      required: true
    },
    searchMode: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      callStatuses: []
    };
  },

  inject: ['lookAt', 'onShowDMVModal'],

  computed: {
    callKey () {
      return this.$_.get(this.call, 'lCallKey', '');
    },

    callStatus () {
      const statusId = this.$_.get(this.call, 'lCallStatusTypeKey', '');
      const status = this.$_.find(this.callStatuses, ['Key', statusId]);

      return status ? status.Value : '';
    },

    hasDmvAccess () {
      return Access.has('dmv.access');
    },

    hasHolds () {
      const holds = this.$_.get(this.call, 'Holds', []);
      const activeHolds = this.$_.filter(holds, hold => !hold.dReleased);

      return activeHolds.length > 0;
    },

    notesProxy () {
      return this.$_.get(this.call, 'Notes', []);
    },

    imagesProxy () {
      return this.$_.get(this.call, 'Images', []);
    },

    shouldShowReferenceNumber () {
      return Number(this.$store.state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT;
    },

    paymentsProxy () {
      return this.$_.get(this.call, 'TowPayments', []);
    },

    priceProxy () {
      return this.priceRepository.sale.Total || this.priceRepository.tow.Total;
    },

    balanceProxy () {
      return this.paymentsProxy.length > 0
        ? this.liveBalance
        : Number(this.$_.get(this.call, 'tcBalance', 0));
    }
  },

  methods: {
    async setCallStatuses () {
      this.callStatuses = await new Promise(resolve => {
        this.$store.dispatch('CALL__getStatuses', {
          callback: response => { resolve(response); }
        });
      });
    }
  },

  mounted () {
    this.setCallStatuses();
  }
};
</script>
